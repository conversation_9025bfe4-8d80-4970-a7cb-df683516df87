import { useEffect, useRef, useState } from 'react';
import { terminalEventBus } from './terminal-event-bus';

// ✅ Fix SSR: Dynamic imports for browser-only xterm.js modules
let Terminal: any;
let FitAddon: any;
let WebLinksAddon: any;

// ✅ Load xterm modules only on client side
const loadXtermModules = async () => {
  if (typeof window === 'undefined') return false;

  try {
    const [terminalModule, fitModule, webLinksModule] = await Promise.all([
      import('@xterm/xterm'),
      import('@xterm/addon-fit'),
      import('@xterm/addon-web-links')
    ]);

    Terminal = terminalModule.Terminal;
    FitAddon = fitModule.FitAddon;
    WebLinksAddon = webLinksModule.WebLinksAddon;

    // Import CSS dynamically
    await import('@xterm/xterm/css/xterm.css');

    return true;
  } catch (error) {
    console.error('Failed to load xterm modules:', error);
    return false;
  }
};

interface TerminalBootstrapProps {
  className?: string;
  onReady?: (terminal: Terminal) => void;
  // ✅ Task 98: Logging callbacks
  onInput?: (input: string) => void;
  onOutput?: (output: string) => void;
  sessionId?: string;
}

export default function TerminalBootstrap({
  className = '',
  onReady,
  onInput,
  onOutput,
  sessionId
}: TerminalBootstrapProps) {
  const terminalRef = useRef<HTMLDivElement>(null);
  const terminalInstance = useRef<any | null>(null);
  const fitAddon = useRef<any | null>(null);
  const webLinksAddon = useRef<any | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const cleanupRef = useRef<(() => void) | null>(null);

  // ✅ SSR Guard: Only render on client side
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted || !terminalRef.current || terminalInstance.current) {
      return;
    }

    const initializeTerminal = async () => {
      try {
        setIsLoading(true);
        setLoadError(null);

        // ✅ Load xterm modules first
        const modulesLoaded = await loadXtermModules();
        if (!modulesLoaded) {
          throw new Error('Failed to load terminal modules');
        }

        // Initialize terminal with enhanced configuration
        const terminal = new Terminal({
          cursorBlink: true,
          fontSize: 13,
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          lineHeight: 1.2,
          theme: {
            background: '#1a1a1a',
            foreground: '#ffffff',
            cursor: '#ffffff',
            selectionBackground: '#3a3a3a',
          },
          cols: 80,
          rows: 24,
          scrollback: 1000, // Enable scrollback for better UX
          allowTransparency: false,
          convertEol: true,
        });

        // Initialize addons
        const fit = new FitAddon();
        const webLinks = new WebLinksAddon();

        // Load addons
        terminal.loadAddon(fit);
        terminal.loadAddon(webLinks);

        // Store references
        terminalInstance.current = terminal;
        fitAddon.current = fit;
        webLinksAddon.current = webLinks;

        // Open terminal in DOM
        terminal.open(terminalRef.current);

        // Fit terminal to container
        fit.fit();

        // Focus terminal
        terminal.focus();

        // Check if we have access to the terminal API
        if (typeof window !== 'undefined' && window.electronAPI?.terminal) {
          // Create PTY process
          const terminalId = await window.electronAPI.terminal.create();
          console.log('✅ TerminalBootstrap: PTY created with ID:', terminalId);

          // Set up data listener for PTY output
          const cleanup = window.electronAPI.terminal.listen(terminalId, (data, exit) => {
            if (exit) {
              // Terminal process exited
              console.log('✅ TerminalBootstrap: Terminal process exited:', exit);
              const exitMessage = `\r\n[Process exited with code ${exit.exitCode}]`;
              terminal.writeln(exitMessage);
              // ✅ Task 98: Log process exit
              onOutput?.(exitMessage);
              setIsReady(false);
            } else if (data) {
              // Write PTY output to terminal
              terminal.write(data);
              // ✅ Task 98: Log terminal output
              onOutput?.(data);
            }
          });

          // Set up input handler to send data to PTY
          terminal.onData((data) => {
            window.electronAPI.terminal.write(terminalId, data);
            // ✅ Task 98: Log user input
            onInput?.(data);
          });

          // Handle terminal resize with debouncing
          let resizeTimeout: NodeJS.Timeout;
          terminal.onResize(({ cols, rows }) => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
              window.electronAPI.terminal.resize(terminalId, cols, rows);
              console.log(`✅ TerminalBootstrap: Resized to ${cols}x${rows}`);
            }, 100);
          });

          // Enhanced resize handling with multiple triggers
          const handleResize = () => {
            if (fit && terminalRef.current) {
              // Small delay to ensure container has updated dimensions
              setTimeout(() => {
                fit.fit();
                terminal.focus();
              }, 10);
            }
          };

          // Listen to multiple resize events
          window.addEventListener('resize', handleResize);

          // Also handle container resize using ResizeObserver if available
          let resizeObserver: ResizeObserver | null = null;
          if (window.ResizeObserver && terminalRef.current) {
            resizeObserver = new ResizeObserver(handleResize);
            resizeObserver.observe(terminalRef.current);
          }

          // ✅ Task 92: Set up agent output listeners
          const agentOutputUnsubscribe = terminalEventBus.on('agent-output', ({ output, agentName, command, success }) => {
            if (terminal) {
              // Format agent output with colors
              const prefix = agentName ? `[${agentName}]` : '[Agent]';
              const statusColor = success ? '\x1b[1;32m' : '\x1b[1;31m'; // Green for success, red for failure
              const resetColor = '\x1b[0m';

              terminal.writeln(`${statusColor}${prefix}:${resetColor} ${output}`);
            }
          });

          const systemMessageUnsubscribe = terminalEventBus.on('system-message', ({ message, type }) => {
            if (terminal) {
              // Format system messages with colors based on type
              let color = '\x1b[1;37m'; // Default white
              switch (type) {
                case 'info':
                  color = '\x1b[1;36m'; // Cyan
                  break;
                case 'success':
                  color = '\x1b[1;32m'; // Green
                  break;
                case 'warning':
                  color = '\x1b[1;33m'; // Yellow
                  break;
                case 'error':
                  color = '\x1b[1;31m'; // Red
                  break;
              }
              const resetColor = '\x1b[0m';

              terminal.writeln(`${color}${message}${resetColor}`);
            }
          });

          const clearTerminalUnsubscribe = terminalEventBus.on('clear-terminal', () => {
            if (terminal) {
              terminal.clear();
            }
          });

          // Mark as ready
          setIsReady(true);
          setIsLoading(false);
          onReady?.(terminal);

          console.log('✅ TerminalBootstrap: Real shell connected successfully');

          // Store cleanup function
          const cleanupFunction = () => {
            clearTimeout(resizeTimeout);
            window.removeEventListener('resize', handleResize);
            resizeObserver?.disconnect();
            cleanup?.();
            // ✅ Task 92: Cleanup event listeners
            agentOutputUnsubscribe();
            systemMessageUnsubscribe();
            clearTerminalUnsubscribe();
            window.electronAPI.terminal.dispose(terminalId);
            if (terminalInstance.current) {
              terminalInstance.current.dispose();
              terminalInstance.current = null;
            }
            fitAddon.current = null;
            webLinksAddon.current = null;
            setIsReady(false);
            console.log('✅ TerminalBootstrap: PTY terminal cleaned up');
          };

          cleanupRef.current = cleanupFunction;
          return cleanupFunction;
        } else {
          // Fallback to echo mode if terminal API is not available
          console.warn('⚠️ TerminalBootstrap: Terminal API not available, using echo mode');

          terminal.writeln('🟡 Terminal API not available - Echo mode only');
          terminal.writeln('📝 Type anything to test echo functionality...');
          terminal.write('\r\n$ ');

          // Simple echo functionality for testing
          terminal.onData((data) => {
            // ✅ Task 98: Log input in echo mode
            onInput?.(data);

            if (data === '\r') {
              const output = '\r\n$ ';
              terminal.write(output);
              // ✅ Task 98: Log echo output
              onOutput?.(output);
            } else if (data === '\u007F') {
              const output = '\b \b';
              terminal.write(output);
              onOutput?.(output);
            } else {
              terminal.write(data);
              onOutput?.(data);
            }
          });

          // Handle resize events
          const handleResize = () => {
            if (fit) {
              fit.fit();
            }
          };

          window.addEventListener('resize', handleResize);

          // ✅ Task 92: Set up agent output listeners for echo mode too
          const agentOutputUnsubscribe = terminalEventBus.on('agent-output', ({ output, agentName, command, success }) => {
            if (terminal) {
              const prefix = agentName ? `[${agentName}]` : '[Agent]';
              const statusColor = success ? '\x1b[1;32m' : '\x1b[1;31m';
              const resetColor = '\x1b[0m';

              terminal.writeln(`${statusColor}${prefix}:${resetColor} ${output}`);
            }
          });

          const systemMessageUnsubscribe = terminalEventBus.on('system-message', ({ message, type }) => {
            if (terminal) {
              let color = '\x1b[1;37m';
              switch (type) {
                case 'info': color = '\x1b[1;36m'; break;
                case 'success': color = '\x1b[1;32m'; break;
                case 'warning': color = '\x1b[1;33m'; break;
                case 'error': color = '\x1b[1;31m'; break;
              }
              const resetColor = '\x1b[0m';

              terminal.writeln(`${color}${message}${resetColor}`);
            }
          });

          const clearTerminalUnsubscribe = terminalEventBus.on('clear-terminal', () => {
            if (terminal) {
              terminal.clear();
            }
          });

          // Mark as ready
          setIsReady(true);
          setIsLoading(false);
          onReady?.(terminal);

          console.log('✅ TerminalBootstrap: Echo mode initialized');

          // Store cleanup function for echo mode
          const cleanupFunction = () => {
            window.removeEventListener('resize', handleResize);
            // ✅ Task 92: Cleanup event listeners for echo mode
            agentOutputUnsubscribe();
            systemMessageUnsubscribe();
            clearTerminalUnsubscribe();
            if (terminalInstance.current) {
              terminalInstance.current.dispose();
              terminalInstance.current = null;
            }
            fitAddon.current = null;
            webLinksAddon.current = null;
            setIsReady(false);
            console.log('✅ TerminalBootstrap: Echo mode cleaned up');
          };

          cleanupRef.current = cleanupFunction;
          return cleanupFunction;
        }
      } catch (error) {
        console.error('❌ TerminalBootstrap: Initialization failed:', error);
        setIsReady(false);
        setIsLoading(false);
        setLoadError(error instanceof Error ? error.message : 'Failed to initialize terminal');
      }
    };

    initializeTerminal();
  }, [isMounted, onReady]);

  // ✅ Additional resize effect for window resize events
  useEffect(() => {
    const handleGlobalResize = () => {
      if (fitAddon.current && terminalInstance.current) {
        // Debounce resize to avoid excessive calls
        setTimeout(() => {
          fitAddon.current?.fit();
          terminalInstance.current?.focus();
        }, 50);
      }
    };

    window.addEventListener('resize', handleGlobalResize);

    // Also trigger initial fit after component mount
    const initialFit = setTimeout(() => {
      if (fitAddon.current) {
        fitAddon.current.fit();
      }
    }, 100);

    return () => {
      window.removeEventListener('resize', handleGlobalResize);
      clearTimeout(initialFit);
    };
  }, []);

  // Handle container click to focus terminal
  const handleContainerClick = () => {
    if (terminalInstance.current) {
      terminalInstance.current.focus();
    }
  };

  // ✅ SSR Guard: Don't render anything on server side
  if (!isMounted) {
    return (
      <div className={`terminal-bootstrap-container relative ${className}`}>
        <div className="w-full h-full min-h-[400px] bg-black text-white overflow-hidden rounded-md border border-gray-700 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-sm text-gray-300">Loading Terminal...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`terminal-bootstrap-container relative ${className}`}>
      <div
        ref={terminalRef}
        id="terminal-container"
        className="w-full h-full min-h-[400px] bg-black text-white overflow-hidden rounded-md border border-gray-700 focus:border-blue-500 transition-colors"
        tabIndex={0}
        onClick={handleContainerClick}
        style={{
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '13px',
          lineHeight: '1.2',
          width: '100%',
          height: '100%',
        }}
      />
      {(isLoading || !isReady) && !loadError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black text-white rounded-md">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-sm text-gray-300">
              {isLoading ? 'Loading Terminal Modules...' : 'Initializing Terminal...'}
            </p>
          </div>
        </div>
      )}
      {loadError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black text-white rounded-md">
          <div className="text-center">
            <div className="text-red-500 text-2xl mb-2">⚠️</div>
            <p className="text-sm text-red-400 mb-2">Terminal Load Error</p>
            <p className="text-xs text-gray-400">{loadError}</p>
          </div>
        </div>
      )}
    </div>
  );
}
