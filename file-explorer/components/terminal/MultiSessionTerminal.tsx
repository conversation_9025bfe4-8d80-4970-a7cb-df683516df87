'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Plus, X, Terminal as TerminalIcon } from 'lucide-react';

// Dynamic imports for xterm (client-side only)
let Terminal: any;
let FitAddon: any;
let WebLinksAddon: any;

// Load xterm modules dynamically
const loadXtermModules = async () => {
  if (typeof window === 'undefined') return false;

  try {
    const [terminalModule, fitModule, webLinksModule] = await Promise.all([
      import('@xterm/xterm'),
      import('@xterm/addon-fit'),
      import('@xterm/addon-web-links')
    ]);

    Terminal = terminalModule.Terminal;
    FitAddon = fitModule.FitAddon;
    WebLinksAddon = webLinksModule.WebLinksAddon;

    // Import CSS dynamically
    await import('@xterm/xterm/css/xterm.css');

    return true;
  } catch (error) {
    console.error('Failed to load xterm modules:', error);
    return false;
  }
};

// ✅ Task 104 Step 3: Terminal Instance Interface
interface TerminalInstance {
  terminal: Terminal;
  fitAddon: FitAddon;
  sessionId: string;
  shell: string;
  createdAt: number;
}

// ✅ Task 104 Step 3: Session Manager Hook
export function useMultiSessionTerminal() {
  const [sessions, setSessions] = useState<Record<string, TerminalInstance>>({});
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);

  const createSession = useCallback(async (shell = '/bin/bash') => {
    try {
      // Load xterm modules if not already loaded
      if (!Terminal) {
        const loaded = await loadXtermModules();
        if (!loaded) {
          console.error('Failed to load xterm modules');
          return null;
        }
      }

      const result = await window.electronAPI?.terminal?.createUserSession(shell);
      if (result?.success && result?.sessionId) {
        const sessionId = result.sessionId;

        // Create terminal instance
        const terminal = new Terminal({
          cursorBlink: true,
          fontSize: 13,
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          lineHeight: 1.2,
          theme: {
            background: '#000000',
            foreground: '#ffffff',
            cursor: '#ffffff',
            selection: '#ffffff40',
          },
          cols: 80,
          rows: 24,
          scrollback: 1000,
          allowTransparency: false,
          convertEol: true,
        });

        const fitAddon = new FitAddon();
        const webLinksAddon = new WebLinksAddon();
        
        terminal.loadAddon(fitAddon);
        terminal.loadAddon(webLinksAddon);

        const newInstance: TerminalInstance = {
          terminal,
          fitAddon,
          sessionId,
          shell,
          createdAt: Date.now()
        };

        setSessions(prev => ({ ...prev, [sessionId]: newInstance }));
        setActiveSessionId(sessionId);
        
        return sessionId;
      } else {
        console.error('Failed to create user session:', result);
        return null;
      }
    } catch (error) {
      console.error('Error creating session:', error);
      return null;
    }
  }, []);

  const disposeSession = useCallback(async (sessionId: string) => {
    try {
      const session = sessions[sessionId];
      if (session) {
        // Dispose terminal instance
        session.terminal.dispose();
        
        // Dispose backend session
        await window.electronAPI?.terminal?.disposeUserSession(sessionId);
        
        // Remove from state
        setSessions(prev => {
          const newSessions = { ...prev };
          delete newSessions[sessionId];
          return newSessions;
        });

        // If this was the active session, switch to another or clear
        if (activeSessionId === sessionId) {
          const remainingSessions = Object.keys(sessions).filter(id => id !== sessionId);
          setActiveSessionId(remainingSessions.length > 0 ? remainingSessions[0] : null);
        }
      }
    } catch (error) {
      console.error('Error disposing session:', error);
    }
  }, [sessions, activeSessionId]);

  const writeToSession = useCallback(async (sessionId: string, data: string) => {
    try {
      await window.electronAPI?.terminal?.writeUserSession(sessionId, data);
    } catch (error) {
      console.error('Error writing to session:', error);
    }
  }, []);

  const resizeSession = useCallback(async (sessionId: string, cols: number, rows: number) => {
    try {
      await window.electronAPI?.terminal?.resizeUserSession(sessionId, cols, rows);
    } catch (error) {
      console.error('Error resizing session:', error);
    }
  }, []);

  const listSessions = useCallback(async () => {
    try {
      const result = await window.electronAPI?.terminal?.listUserSessions();
      return result?.success ? result.sessions : [];
    } catch (error) {
      console.error('Error listing sessions:', error);
      return [];
    }
  }, []);

  return {
    sessions,
    activeSessionId,
    setActiveSessionId,
    createSession,
    disposeSession,
    writeToSession,
    resizeSession,
    listSessions
  };
}

// ✅ Task 104 Step 4: Multi-Session Terminal Component
interface MultiSessionTerminalProps {
  className?: string;
  onReady?: (terminal: Terminal) => void;
}

export default function MultiSessionTerminal({ 
  className = '', 
  onReady 
}: MultiSessionTerminalProps) {
  const {
    sessions,
    activeSessionId,
    setActiveSessionId,
    createSession,
    disposeSession,
    writeToSession,
    resizeSession
  } = useMultiSessionTerminal();

  const terminalRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Get active session
  const activeSession = activeSessionId ? sessions[activeSessionId] : null;

  // Create initial session on mount
  useEffect(() => {
    if (Object.keys(sessions).length === 0) {
      createSession();
    }
  }, [sessions, createSession]);

  // Handle terminal mounting and data flow
  useEffect(() => {
    if (activeSession && terminalRef.current) {
      const { terminal, fitAddon, sessionId } = activeSession;
      
      // Mount terminal to DOM
      terminal.open(terminalRef.current);
      fitAddon.fit();
      terminal.focus();

      // Set up data listeners
      const dataCleanup = window.electronAPI?.terminal?.onUserSessionData(sessionId, (data: string) => {
        terminal.write(data);
      });

      const exitCleanup = window.electronAPI?.terminal?.onUserSessionExit(sessionId, (exitInfo: any) => {
        terminal.writeln(`\r\n[Process exited with code ${exitInfo.exitCode}]`);
      });

      // Set up input handler
      const inputDisposable = terminal.onData((data: string) => {
        writeToSession(sessionId, data);
      });

      // Set up resize handler
      const resizeDisposable = terminal.onResize(({ cols, rows }) => {
        resizeSession(sessionId, cols, rows);
      });

      // Call onReady callback
      onReady?.(terminal);

      // Cleanup function
      return () => {
        dataCleanup?.();
        exitCleanup?.();
        inputDisposable?.dispose();
        resizeDisposable?.dispose();
      };
    }
  }, [activeSession, onReady, writeToSession, resizeSession]);

  // Handle new session creation
  const handleCreateSession = async () => {
    setIsLoading(true);
    await createSession();
    setIsLoading(false);
  };

  // Handle session close
  const handleCloseSession = async (sessionId: string) => {
    await disposeSession(sessionId);
  };

  // Handle session switch
  const handleSwitchSession = (sessionId: string) => {
    setActiveSessionId(sessionId);
  };

  const sessionList = Object.values(sessions);

  return (
    <div className={`multi-session-terminal ${className}`}>
      {/* ✅ Task 104 Step 4: Tab Bar */}
      <div className="terminal-tabs bg-gray-800 border-b border-gray-700 flex items-center gap-1 px-2 py-1">
        {sessionList.map((session, index) => (
          <div
            key={session.sessionId}
            className={`terminal-tab flex items-center gap-2 px-3 py-1 rounded-t-md cursor-pointer text-sm ${
              session.sessionId === activeSessionId
                ? 'bg-gray-900 text-white border-b-2 border-blue-500'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            onClick={() => handleSwitchSession(session.sessionId)}
          >
            <TerminalIcon className="w-3 h-3" />
            <span>Session {index + 1}</span>
            {sessionList.length > 1 && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleCloseSession(session.sessionId);
                }}
                className="ml-1 hover:bg-gray-500 rounded p-0.5"
              >
                <X className="w-3 h-3" />
              </button>
            )}
          </div>
        ))}
        <button
          onClick={handleCreateSession}
          disabled={isLoading}
          className="flex items-center gap-1 px-2 py-1 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded"
        >
          <Plus className="w-3 h-3" />
          New
        </button>
      </div>

      {/* ✅ Task 104 Step 4: Terminal Content */}
      <div className="terminal-content flex-1">
        {activeSessionId ? (
          <div
            ref={terminalRef}
            className="w-full h-full min-h-[400px] bg-black text-white overflow-hidden"
            style={{
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: '13px',
              lineHeight: 1.2,
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <TerminalIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No terminal sessions</p>
              <button
                onClick={handleCreateSession}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Create New Terminal
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
