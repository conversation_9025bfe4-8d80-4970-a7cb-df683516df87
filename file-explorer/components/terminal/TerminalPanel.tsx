"use client"

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>r, <PERSON><PERSON>, Wrench, TestTube, Terminal as TerminalIcon, Settings, Plus, X, FileText, Download } from 'lucide-react';
import TerminalBootstrap from './TerminalBootstrap';
import { terminalEventBus } from './terminal-event-bus';
import { terminalSessionLoggingService } from '../services/terminal-session-logging-service';

// ✅ Task 94: Agent configuration for terminal routing
interface AgentConfig {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  capabilities: string[];
}

// ✅ Task 97: Terminal session management types
// ✅ Task 98: Extended with command output logging
interface TerminalSession {
  id: string;
  name: string;
  terminal: any; // xterm Terminal instance
  ptyId?: string; // Backend PTY process ID
  isActive: boolean;
  createdAt: number;
  // Session-specific state
  commandHistory: string[];
  historyIndex: number;
  currentInput: string;
  activeAgentId: string;
  isAgentMode: boolean;
  // ✅ Task 98: Session logging
  log: string[]; // Store all input/output logs line by line
  logEnabled: boolean; // Toggle logging per session
}

const AVAILABLE_AGENTS: AgentConfig[] = [
  {
    id: 'micromanager',
    name: 'Micromanager',
    description: 'Task orchestration and decomposition',
    icon: <Settings className="w-4 h-4" />,
    color: 'bg-purple-500',
    capabilities: ['Task Planning', 'Coordination', 'Resource Management']
  },
  {
    id: 'intern',
    name: 'Intern Agent',
    description: 'Basic tasks and learning',
    icon: <User className="w-4 h-4" />,
    color: 'bg-green-500',
    capabilities: ['Simple Tasks', 'Documentation', 'Basic Operations']
  },
  {
    id: 'senior-agent',
    name: 'Senior Agent',
    description: 'Complex development tasks',
    icon: <Bot className="w-4 h-4" />,
    color: 'bg-blue-500',
    capabilities: ['Code Review', 'Architecture', 'Complex Problem Solving']
  },
  {
    id: 'designer',
    name: 'Designer Agent',
    description: 'UI/UX design and prototyping',
    icon: <Wrench className="w-4 h-4" />,
    color: 'bg-pink-500',
    capabilities: ['UI Design', 'Prototyping', 'User Experience']
  },
  {
    id: 'tester',
    name: 'Tester Agent',
    description: 'Testing and quality assurance',
    icon: <TestTube className="w-4 h-4" />,
    color: 'bg-orange-500',
    capabilities: ['Test Automation', 'Quality Assurance', 'Bug Detection']
  }
];

interface TerminalPanelProps {
  className?: string;
  onReady?: (terminal: any) => void;
  defaultAgentId?: string;
}

export default function TerminalPanel({
  className = '',
  onReady,
  defaultAgentId = 'senior-agent'
}: TerminalPanelProps) {
  // ✅ Task 97: Multi-session terminal state
  const [sessions, setSessions] = useState<TerminalSession[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const terminalContainerRef = useRef<HTMLDivElement>(null);

  // Get current active session
  const activeSession = sessions.find(session => session.id === activeSessionId);

  // Legacy state for backward compatibility (derived from active session)
  const activeAgentId = activeSession?.activeAgentId || defaultAgentId;
  const isAgentMode = activeSession?.isAgentMode || false;
  const terminalInstance = activeSession?.terminal || null;
  const commandHistory = activeSession?.commandHistory || [];
  const historyIndex = activeSession?.historyIndex || -1;
  const currentInput = activeSession?.currentInput || '';
  const inputBufferRef = useRef<string>('');

  // ✅ Task 96: Known commands for autocomplete
  const KNOWN_COMMANDS = [
    // General commands
    'help', 'clear', 'exit', 'pwd', 'ls', 'cd', 'cat', 'echo', 'whoami', 'date',
    // Agent-specific commands
    'analyze', 'build', 'test', 'run', 'deploy', 'explain', 'summarize',
    'implement', 'create', 'develop', 'design', 'optimize', 'refactor',
    'debug', 'fix', 'integrate', 'validate', 'verify', 'check',
    // File operations
    'file', 'write', 'read', 'modify', 'delete', 'generate',
    // UI/Design commands
    'ui', 'interface', 'component', 'style', 'layout', 'theme',
    // Testing commands
    'spec', 'qa', 'quality', 'coverage', 'automation'
  ];

  const activeAgent = AVAILABLE_AGENTS.find(agent => agent.id === activeAgentId);

  // ✅ Task 97: Session management functions
  const updateSessionState = (sessionId: string, updates: Partial<TerminalSession>) => {
    setSessions(prev => prev.map(session =>
      session.id === sessionId ? { ...session, ...updates } : session
    ));
  };

  const createNewSession = useCallback(async () => {
    const sessionId = `terminal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const sessionName = `Terminal ${sessions.length + 1}`;

    // Create new session object (terminal will be set when TerminalBootstrap is ready)
    const newSession: TerminalSession = {
      id: sessionId,
      name: sessionName,
      terminal: null,
      ptyId: undefined,
      isActive: true,
      createdAt: Date.now(),
      commandHistory: [],
      historyIndex: -1,
      currentInput: '',
      activeAgentId: defaultAgentId,
      isAgentMode: false,
      // ✅ Task 98: Initialize logging properties
      log: [],
      logEnabled: true // Enable logging by default
    };

    // Deactivate all other sessions
    setSessions(prev => [
      ...prev.map(session => ({ ...session, isActive: false })),
      newSession
    ]);

    setActiveSessionId(sessionId);

    // ✅ Task 98: Initialize session logging
    terminalSessionLoggingService.initializeSessionLog(sessionId, sessionName);

    return sessionId;
  }, [sessions.length, defaultAgentId]);

  const closeSession = useCallback((sessionId: string) => {
    const sessionToClose = sessions.find(s => s.id === sessionId);
    if (sessionToClose?.terminal) {
      sessionToClose.terminal.dispose();
    }

    // ✅ Task 98: Complete session logging before closing
    terminalSessionLoggingService.completeSessionLog(sessionId);

    const remainingSessions = sessions.filter(s => s.id !== sessionId);
    setSessions(remainingSessions);

    if (sessionId === activeSessionId) {
      if (remainingSessions.length > 0) {
        const newActiveSession = remainingSessions[0];
        setActiveSessionId(newActiveSession.id);
        updateSessionState(newActiveSession.id, { isActive: true });
      } else {
        setActiveSessionId(null);
      }
    }
  }, [sessions, activeSessionId]);

  const switchToSession = useCallback((sessionId: string) => {
    // Deactivate current session
    if (activeSessionId) {
      updateSessionState(activeSessionId, { isActive: false });
    }

    // Activate new session
    updateSessionState(sessionId, { isActive: true });
    setActiveSessionId(sessionId);
  }, [activeSessionId]);

  // ✅ Task 98: Session logging utility functions
  const appendToSessionLog = useCallback((sessionId: string, entry: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session && session.logEnabled) {
      // Update local session log
      updateSessionState(sessionId, {
        log: [...session.log, entry]
      });

      // Also log to the logging service for export/viewing
      terminalSessionLoggingService.addLogEntry(sessionId, entry, 'output');
    }
  }, [sessions]);

  const logUserInput = useCallback((sessionId: string, input: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session && session.logEnabled) {
      const formattedInput = `> ${input}`;
      updateSessionState(sessionId, {
        log: [...session.log, formattedInput]
      });

      terminalSessionLoggingService.logInput(sessionId, input);
    }
  }, [sessions]);

  const toggleSessionLogging = useCallback((sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      const newLogEnabled = !session.logEnabled;
      updateSessionState(sessionId, { logEnabled: newLogEnabled });

      // Log the toggle action
      const message = newLogEnabled ? 'Logging enabled' : 'Logging disabled';
      terminalSessionLoggingService.addLogEntry(sessionId, `[SYSTEM] ${message}`, 'system');
    }
  }, [sessions]);

  const viewSessionLog = useCallback((sessionId: string) => {
    const sessionLog = terminalSessionLoggingService.getSessionLog(sessionId);
    if (sessionLog) {
      console.log(`📜 Log for ${sessionLog.sessionName}:`, sessionLog.entries);
      // Could also open a modal or new window here
    }
  }, []);

  const exportSessionLog = useCallback((sessionId: string) => {
    const logContent = terminalSessionLoggingService.exportSessionLog(sessionId);
    if (logContent) {
      const session = sessions.find(s => s.id === sessionId);
      const filename = `terminal-log-${session?.name || sessionId}-${new Date().toISOString().slice(0, 10)}.txt`;

      // Create and download file
      const blob = new Blob([logContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  }, [sessions]);

  // ✅ Task 96: Helper functions for input management (updated for sessions)
  const updateTerminalInput = (newInput: string) => {
    if (!terminalInstance || !activeSessionId) return;

    // Clear current line and redraw with new input
    const promptLength = 2; // "$ " length
    const currentLineLength = promptLength + currentInput.length;

    // Move cursor to beginning of input and clear line
    terminalInstance.write('\r' + ' '.repeat(currentLineLength) + '\r$ ' + newInput);

    // Update session state
    updateSessionState(activeSessionId, { currentInput: newInput });
    inputBufferRef.current = newInput;
  };

  const addToHistory = (command: string) => {
    if (!activeSessionId) return;

    if (command.trim() && !commandHistory.includes(command.trim())) {
      const newHistory = [...commandHistory, command.trim()];
      updateSessionState(activeSessionId, {
        commandHistory: newHistory,
        historyIndex: -1
      });
    }
  };

  const navigateHistory = (direction: 'up' | 'down') => {
    if (!activeSessionId) return;

    if (direction === 'up') {
      if (commandHistory.length > 0 && historyIndex < commandHistory.length - 1) {
        const newIndex = historyIndex + 1;
        const historyCommand = commandHistory[commandHistory.length - 1 - newIndex];
        updateSessionState(activeSessionId, { historyIndex: newIndex });
        updateTerminalInput(historyCommand);
      }
    } else {
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        const historyCommand = commandHistory[commandHistory.length - 1 - newIndex];
        updateSessionState(activeSessionId, { historyIndex: newIndex });
        updateTerminalInput(historyCommand);
      } else if (historyIndex === 0) {
        updateSessionState(activeSessionId, { historyIndex: -1 });
        updateTerminalInput('');
      }
    }
  };

  const autocompleteCommand = () => {
    const input = currentInput.trim();
    if (!input) return;

    // Find matching commands
    const matches = KNOWN_COMMANDS.filter(cmd => cmd.startsWith(input.toLowerCase()));

    if (matches.length === 1) {
      // Single match - complete it
      updateTerminalInput(matches[0]);
    } else if (matches.length > 1) {
      // Multiple matches - show them
      terminalInstance?.writeln(`\r\n💡 Available completions: ${matches.join(', ')}`);
      terminalInstance?.write('$ ' + currentInput);
    }
  };

  // ✅ Task 94 Step 1: Handle terminal ready event (updated for sessions)
  const handleTerminalReady = (terminal: any) => {
    // Update the active session with the terminal instance
    if (activeSessionId) {
      updateSessionState(activeSessionId, { terminal });
    }
    onReady?.(terminal);

    // ✅ Task 94 Step 2: Set up input interception for agent routing
    terminal.onData((input: string) => {
      // ✅ Task 98: Log user input if not in agent mode
      if (!isAgentMode && activeSessionId) {
        logUserInput(activeSessionId, input);
      }

      if (isAgentMode) {
        handleAgentInput(input);
      }
      // Note: Normal PTY input is handled by TerminalBootstrap
    });

    // Display initial agent mode status
    if (isAgentMode) {
      displayAgentModeStatus(terminal);
    }
  };

  // ✅ Task 94 Step 2: Handle input routing to agents (Enhanced with Task 96 features)
  const handleAgentInput = (input: string) => {
    if (!activeSessionId) return;

    // ✅ Task 96: Handle special keys for history and autocomplete
    if (input === '\r') {
      // Enter key - process command
      if (currentInput.trim()) {
        // ✅ Task 98: Log agent command input
        logUserInput(activeSessionId, currentInput.trim());

        addToHistory(currentInput.trim());
        processAgentCommand(currentInput.trim());
        updateSessionState(activeSessionId, { currentInput: '' });
        inputBufferRef.current = '';
      }
      terminalInstance?.write('\r\n$ ');
    } else if (input === '\u007F') {
      // Backspace
      if (currentInput.length > 0) {
        const newInput = currentInput.slice(0, -1);
        updateTerminalInput(newInput);
      }
    } else if (input === '\u0003') {
      // Ctrl+C - cancel current input
      updateSessionState(activeSessionId, {
        currentInput: '',
        historyIndex: -1
      });
      inputBufferRef.current = '';
      terminalInstance?.write('^C\r\n$ ');
    } else if (input === '\t') {
      // ✅ Task 96: Tab key - autocomplete
      autocompleteCommand();
    } else if (input === '\u001b[A') {
      // ✅ Task 96: Arrow Up - navigate history up
      navigateHistory('up');
    } else if (input === '\u001b[B') {
      // ✅ Task 96: Arrow Down - navigate history down
      navigateHistory('down');
    } else if (input === '\u001b[C' || input === '\u001b[D') {
      // Arrow Left/Right - ignore for now (could implement cursor movement later)
      return;
    } else if (input.charCodeAt(0) >= 32 && input.charCodeAt(0) <= 126) {
      // Regular printable character
      const newInput = currentInput + input;
      updateTerminalInput(newInput);
    }
  };

  // ✅ Task 94 Step 3: Process agent commands
  const processAgentCommand = async (command: string) => {
    if (!activeAgent || !terminalInstance) return;

    try {
      // Display command being processed
      terminalInstance.writeln(`\r\n🤖 [${activeAgent.name}] Processing: ${command}`);

      // ✅ Task 96: Create enhanced context with command history
      const context = {
        input: command,
        agentId: activeAgentId,
        timestamp: Date.now(),
        // ✅ Task 96: Include command history for agent awareness
        previousCommands: commandHistory.slice(-5), // Last 5 commands
        historyContext: {
          totalCommands: commandHistory.length,
          recentCommands: commandHistory.slice(-3),
          isRepeatedCommand: commandHistory.includes(command)
        }
      };

      // ✅ Task 94 Step 3: Route to agent via message bus or agent manager
      terminalEventBus.emit('terminal:manual-command', context);

      // Show processing indicator with history context
      if (commandHistory.includes(command)) {
        terminalInstance.writeln(`🔄 Repeated command detected - routing to ${activeAgent.name}...`);
      } else {
        terminalInstance.writeln(`⏳ Routing command to ${activeAgent.name}...`);
      }

    } catch (error) {
      terminalInstance.writeln(`❌ Error processing command: ${error instanceof Error ? error.message : String(error)}`);
      terminalInstance.write('$ ');
    }
  };

  // ✅ Display agent mode status
  const displayAgentModeStatus = (terminal: any) => {
    if (!activeAgent) return;

    terminal.writeln('\r\n🤖 Agent Terminal Mode Activated');
    terminal.writeln(`📋 Active Agent: ${activeAgent.name}`);
    terminal.writeln(`📝 Description: ${activeAgent.description}`);
    terminal.writeln(`🔧 Capabilities: ${activeAgent.capabilities.join(', ')}`);
    terminal.writeln('💡 Type commands to route them to the selected agent');
    terminal.writeln('🔄 Use the dropdown above to switch agents');
    terminal.writeln('⚡ Toggle "Agent Mode" to switch between direct terminal and agent routing');
    terminal.write('\r\n$ ');
  };

  // ✅ Handle agent mode toggle (updated for sessions)
  const handleAgentModeToggle = () => {
    if (!activeSessionId) return;

    const newMode = !isAgentMode;
    updateSessionState(activeSessionId, { isAgentMode: newMode });

    if (terminalInstance) {
      if (newMode) {
        terminalInstance.writeln('\r\n🤖 Switched to Agent Mode');
        displayAgentModeStatus(terminalInstance);
      } else {
        terminalInstance.writeln('\r\n💻 Switched to Direct Terminal Mode');
        terminalInstance.writeln('Commands will now be executed directly in the shell');
      }
    }
  };

  // ✅ Handle agent selection change (updated for sessions)
  const handleAgentChange = (newAgentId: string) => {
    if (!activeSessionId) return;

    updateSessionState(activeSessionId, { activeAgentId: newAgentId });
    const newAgent = AVAILABLE_AGENTS.find(agent => agent.id === newAgentId);

    if (terminalInstance && isAgentMode && newAgent) {
      terminalInstance.writeln(`\r\n🔄 Switched to ${newAgent.name}`);
      terminalInstance.writeln(`📝 ${newAgent.description}`);
      terminalInstance.write('$ ');
    }
  };

  // ✅ Task 94 Step 4: Listen for agent responses
  useEffect(() => {
    const unsubscribe = terminalEventBus.on('terminal:agent-output', ({ output, agentId, success, status }) => {
      if (terminalInstance && agentId === activeAgentId) {
        // ✅ Task 95: Handle different response statuses with appropriate colors
        if (status === 'unsupported') {
          // Yellow color for unsupported commands
          terminalInstance.write(`\r\n\x1b[33m🧠 ${activeAgent?.name} does not support this command\x1b[0m\r\n`);
          terminalInstance.writeln(`\x1b[33m${output}\x1b[0m`);
        } else if (status === 'delegated') {
          // Blue color for delegated commands
          terminalInstance.write(`\r\n\x1b[34m🔄 ${activeAgent?.name} delegated command\x1b[0m\r\n`);
          terminalInstance.writeln(`\x1b[34m${output}\x1b[0m`);
        } else if (success === false) {
          // Red color for failed commands
          terminalInstance.write(`\r\n\x1b[31m❌ Error from ${activeAgent?.name}:\x1b[0m\r\n`);
          terminalInstance.writeln(`\x1b[31m${output}\x1b[0m`);
        } else {
          // Green color for successful commands
          terminalInstance.writeln(`\r\n\x1b[32m✅ Response from ${activeAgent?.name}:\x1b[0m`);
          terminalInstance.writeln(output);
        }
        terminalInstance.write('\r\n$ ');
      }
    });

    return unsubscribe;
  }, [terminalInstance, activeAgentId, activeAgent]);

  // ✅ Task 97: Initialize first session on mount
  useEffect(() => {
    if (sessions.length === 0) {
      createNewSession();
    }
  }, [sessions.length, createNewSession]);

  return (
    <div className={`terminal-panel-container ${className}`}>
      {/* ✅ Task 97: Session Tab Bar */}
      <div className="terminal-tabs bg-gray-800 border-b border-gray-700 flex items-center gap-1 px-2 py-1">
        {sessions.map((session) => (
          <div
            key={session.id}
            className={`terminal-tab flex items-center gap-2 px-3 py-1 rounded-t-md cursor-pointer text-sm ${
              session.id === activeSessionId
                ? 'bg-gray-900 text-white border-b-2 border-blue-500'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            onClick={() => switchToSession(session.id)}
          >
            <TerminalIcon className="w-3 h-3" />
            <span>{session.name}</span>
            {sessions.length > 1 && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  closeSession(session.id);
                }}
                className="ml-1 hover:bg-gray-500 rounded p-0.5"
              >
                <X className="w-3 h-3" />
              </button>
            )}
          </div>
        ))}

        {/* New Terminal Button */}
        <button
          onClick={createNewSession}
          className="flex items-center gap-1 px-2 py-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded text-sm"
          title="New Terminal"
        >
          <Plus className="w-3 h-3" />
          <span className="hidden sm:inline">New</span>
        </button>
      </div>

      {/* ✅ Task 94 Step 1: Agent Context Selector */}
      <div className="terminal-header bg-gray-900 border-b border-gray-700 p-3 flex items-center gap-3">
        <div className="flex items-center gap-2">
          <TerminalIcon className="w-5 h-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-300">Terminal</span>
        </div>

        <div className="flex items-center gap-3 ml-auto">
          {/* Agent Mode Toggle */}
          <Button
            variant={isAgentMode ? "default" : "outline"}
            size="sm"
            onClick={handleAgentModeToggle}
            className="text-xs"
          >
            {isAgentMode ? '🤖 Agent Mode' : '💻 Direct Mode'}
          </Button>

          {/* Agent Selector */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-400">Agent:</span>
            <Select value={activeAgentId} onValueChange={handleAgentChange}>
              <SelectTrigger className="w-48 h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {AVAILABLE_AGENTS.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id}>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${agent.color}`} />
                      {agent.icon}
                      <span>{agent.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Active Agent Badge */}
          {activeAgent && (
            <Badge variant="secondary" className="text-xs">
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${activeAgent.color}`} />
                {activeAgent.name}
              </div>
            </Badge>
          )}

          {/* ✅ Task 98: Session Logging Controls */}
          {activeSessionId && (
            <div className="flex items-center gap-2 border-l border-gray-600 pl-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleSessionLogging(activeSessionId)}
                className="text-xs h-7 px-2"
                title={activeSession?.logEnabled ? 'Disable logging' : 'Enable logging'}
              >
                {activeSession?.logEnabled ? '📝' : '📝❌'}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => viewSessionLog(activeSessionId)}
                className="text-xs h-7 px-2"
                title="View session log"
              >
                <FileText className="w-3 h-3" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => exportSessionLog(activeSessionId)}
                className="text-xs h-7 px-2"
                title="Export session log"
              >
                <Download className="w-3 h-3" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Terminal Content */}
      <div className="terminal-content flex-1" ref={terminalContainerRef}>
        {activeSessionId ? (
          <TerminalBootstrap
            key={activeSessionId} // Force re-mount when session changes
            className="h-full"
            onReady={handleTerminalReady}
            sessionId={activeSessionId}
            onInput={(input) => {
              // ✅ Task 98: Log input to session logging service
              if (activeSession?.logEnabled) {
                terminalSessionLoggingService.addLogEntry(activeSessionId, input, 'input');
              }
            }}
            onOutput={(output) => {
              // ✅ Task 98: Log output to session logging service
              if (activeSession?.logEnabled) {
                terminalSessionLoggingService.addLogEntry(activeSessionId, output, 'output');
              }
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <TerminalIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No terminal sessions</p>
              <button
                onClick={createNewSession}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Create New Terminal
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
