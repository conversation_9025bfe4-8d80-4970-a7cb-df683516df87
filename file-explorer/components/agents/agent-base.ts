// components/agents/agent-base.ts
import { llmResponseCache } from './llm-response-cache';
import { agentContextMemory, ContextMemoryEntry } from './agent-context-memory';

export interface AgentConfig {
  id: string;
  name: string;
  type: string;
  model?: string;
  provider?: 'openai' | 'anthropic' | 'openrouter' | 'azure' | 'google' | 'deepseek' | 'fireworks' | 'mcp';
  maxTokens?: number;
  temperature?: number;
  // ✅ MCP Protocol Support
  useMCP?: boolean;
  mcpConfig?: {
    serverId: string;
    serverCommand?: string;
    serverArgs?: string[];
    fallbackToLLM?: boolean;
  };
}

export interface AgentContext {
  task: string;
  files?: string[];
  codeContext?: string;
  rules?: string[];
  dependencies?: string[];
  projectPath?: string; // ✅ NEW FIELD - Active project directory for file operations
  // ✅ Task 99: Terminal execution support
  terminalSessionId?: string; // Link agent actions to a specific terminal session
  metadata?: Record<string, any> & { kanbanCardId?: string; originalTaskId?: string; }; // Added kanbanCardId and originalTaskId
}

// Moved from agent-manager-complete.ts
export interface AgentStatus {
  id: string;
  name: string;
  type: 'orchestrator' | 'implementation' | 'specialized' | 'middleware';
  status: 'idle' | 'busy' | 'error' | 'offline';
  currentTask?: string;
  lastActivity: number;
  tokensUsed: number;
  tasksCompleted: number;
  errorCount: number;
  healthScore: number; // 0-100
  capabilities: string[];
}

// Moved from agent-manager-complete.ts
export interface TaskAssignment {
  taskId: string;
  agentId: string;
  context: AgentContext;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  deadline?: number;
  retryCount: number;
  maxRetries: number;
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'failed' | 'escalated';
  createdAt: number;
  updatedAt: number;
  escalationReason?: string;
  kanbanCardId?: string; // Add link to Kanban card
}

export interface AgentResponse {
  success: boolean;
  content?: string;
  error?: string;
  tokensUsed?: number;
  executionTime?: number;
  suggestions?: string[];
  metadata?: Record<string, any>;
  // ✅ Task 95: Add status for unknown command handling
  status?: 'success' | 'failed' | 'unsupported' | 'delegated';
  output?: string; // Alternative to content for terminal compatibility
}

// ✅ Task 76: Enhanced execution result interface for structured feedback
export interface AgentExecutionResult {
  status: 'success' | 'error' | 'partial' | 'timeout';
  message?: string;
  outputs?: string[];
  createdFiles?: string[];
  modifiedFiles?: string[];
  notes?: string;
  errorDetails?: {
    type: string;
    code?: string;
    recoverable: boolean;
  };
  metrics?: {
    tokensUsed: number;
    executionTime: number;
    memoryUsage?: number;
  };
  nextActions?: string[];
}

export interface AgentMessage {
  agentId: string;
  taskId: string;
  type: 'update' | 'question' | 'completion' | 'error';
  message: string;
  severity?: 'low' | 'medium' | 'high';
  actions?: string[];
  timestamp: number;
}

export abstract class AgentBase {
  protected config: AgentConfig;
  protected context: AgentContext | null = null;
  protected conversationHistory: AgentMessage[] = [];

  constructor(config: AgentConfig) {
    this.config = config;
  }

  // Abstract methods that must be implemented by subclasses
  abstract execute(context: AgentContext): Promise<AgentResponse>;
  abstract getCapabilities(): string[];
  abstract getSystemPrompt(): string;

  // ✅ Task 95: Abstract method for command support checking
  abstract supportsCommand(command: string): boolean;

  // Common functionality for all agents
  public getId(): string {
    return this.config.id;
  }

  public getName(): string {
    return this.config.name;
  }

  public getType(): string {
    return this.config.type;
  }

  public setContext(context: AgentContext): void {
    this.context = context;
  }

  public getContext(): AgentContext | null {
    return this.context;
  }

  public addMessage(message: Omit<AgentMessage, 'agentId' | 'timestamp'>): void {
    const agentMessage: AgentMessage = {
      ...message,
      agentId: this.config.id,
      timestamp: Date.now()
    };
    this.conversationHistory.push(agentMessage);
  }

  public getConversationHistory(): AgentMessage[] {
    return [...this.conversationHistory];
  }

  public clearHistory(): void {
    this.conversationHistory = [];
  }

  // Utility method for creating structured communication messages
  protected createMessage(
    taskId: string,
    type: AgentMessage['type'],
    message: string,
    severity?: AgentMessage['severity'],
    actions?: string[]
  ): AgentMessage {
    return {
      agentId: this.config.id,
      taskId,
      type,
      message,
      severity,
      actions,
      timestamp: Date.now()
    };
  }

  // Method to validate context before execution
  protected validateContext(context: AgentContext): { valid: boolean; error?: string } {
    if (!context.task || context.task.trim().length === 0) {
      return { valid: false, error: 'Task description is required' };
    }
    return { valid: true };
  }

  // Method to estimate token usage (to be overridden by specific agents)
  protected estimateTokens(context: AgentContext): number {
    const baseTokens = context.task.length / 4; // Rough estimate: 4 chars per token
    const contextTokens = context.codeContext ? context.codeContext.length / 4 : 0;
    const rulesTokens = context.rules ? context.rules.join(' ').length / 4 : 0;
    return Math.ceil(baseTokens + contextTokens + rulesTokens);
  }

  // Method to create error response
  protected createErrorResponse(error: string, tokensUsed = 0): AgentResponse {
    return {
      success: false,
      error,
      tokensUsed,
      executionTime: 0
    };
  }

  // Method to create success response
  protected createSuccessResponse(
    content: string,
    tokensUsed = 0,
    executionTime = 0,
    suggestions?: string[],
    metadata?: Record<string, any>
  ): AgentResponse {
    return {
      success: true,
      content,
      output: content,
      status: 'success',
      tokensUsed,
      executionTime,
      suggestions,
      metadata
    };
  }

  // ✅ Task 95: Method to create unsupported command response
  protected createUnsupportedResponse(
    command: string,
    suggestions?: string[],
    recommendedAgent?: string
  ): AgentResponse {
    const agentName = this.getName();
    let message = `🧠 ${agentName} does not recognize the command: "${command}"`;

    if (recommendedAgent) {
      message += `\n👉 Suggestion: Try using the '${recommendedAgent}' agent for this type of command.`;
    } else {
      message += `\n👉 Suggestion: Try using the 'micromanager' agent for general commands or check available capabilities.`;
    }

    if (suggestions && suggestions.length > 0) {
      message += `\n💡 Available commands: ${suggestions.join(', ')}`;
    }

    return {
      success: false,
      output: message,
      status: 'unsupported',
      tokensUsed: 0,
      executionTime: 0,
      suggestions,
      metadata: {
        unsupportedCommand: command,
        agentId: this.getId(),
        recommendedAgent
      }
    };
  }

  // ✅ Task 95: Method to create delegated response
  protected createDelegatedResponse(
    command: string,
    targetAgent: string,
    reason?: string
  ): AgentResponse {
    const agentName = this.getName();
    let message = `🔄 ${agentName} is delegating command "${command}" to ${targetAgent}`;

    if (reason) {
      message += `\n📝 Reason: ${reason}`;
    }

    return {
      success: true,
      output: message,
      status: 'delegated',
      tokensUsed: 0,
      executionTime: 0,
      metadata: {
        delegatedCommand: command,
        sourceAgent: this.getId(),
        targetAgent,
        reason
      }
    };
  }

  // ✅ Task 95: Helper method to analyze command and recommend agent
  protected analyzeCommandAndRecommendAgent(command: string): {
    recommendedAgent?: string;
    reason?: string;
    suggestions?: string[];
  } {
    const lowerCommand = command.toLowerCase();

    // File operations
    if (lowerCommand.includes('file') || lowerCommand.includes('create') ||
        lowerCommand.includes('write') || lowerCommand.includes('read') ||
        lowerCommand.includes('delete') || lowerCommand.includes('modify')) {
      return {
        recommendedAgent: 'senior-agent',
        reason: 'File operations are best handled by Senior Agent',
        suggestions: ['create file', 'read file', 'modify file', 'delete file']
      };
    }

    // Design and UI tasks
    if (lowerCommand.includes('design') || lowerCommand.includes('ui') ||
        lowerCommand.includes('interface') || lowerCommand.includes('layout') ||
        lowerCommand.includes('style') || lowerCommand.includes('component')) {
      return {
        recommendedAgent: 'designer',
        reason: 'Design and UI tasks are specialized for Designer Agent',
        suggestions: ['design component', 'create layout', 'style interface', 'ui mockup']
      };
    }

    // Testing tasks
    if (lowerCommand.includes('test') || lowerCommand.includes('spec') ||
        lowerCommand.includes('verify') || lowerCommand.includes('validate') ||
        lowerCommand.includes('check') || lowerCommand.includes('assert')) {
      return {
        recommendedAgent: 'tester',
        reason: 'Testing and validation tasks are handled by Tester Agent',
        suggestions: ['write tests', 'validate code', 'check functionality', 'create specs']
      };
    }

    // Simple tasks
    if (lowerCommand.includes('simple') || lowerCommand.includes('basic') ||
        lowerCommand.includes('help') || lowerCommand.includes('learn') ||
        lowerCommand.includes('tutorial') || lowerCommand.includes('example')) {
      return {
        recommendedAgent: 'intern',
        reason: 'Simple and learning tasks are perfect for Intern Agent',
        suggestions: ['help with', 'learn about', 'simple task', 'basic example']
      };
    }

    // Complex orchestration
    if (lowerCommand.includes('orchestrate') || lowerCommand.includes('manage') ||
        lowerCommand.includes('coordinate') || lowerCommand.includes('plan') ||
        lowerCommand.includes('organize') || lowerCommand.includes('workflow')) {
      return {
        recommendedAgent: 'micromanager',
        reason: 'Complex orchestration and planning tasks require Micromanager',
        suggestions: ['plan project', 'orchestrate tasks', 'manage workflow', 'coordinate team']
      };
    }

    // Default to micromanager for general commands
    return {
      recommendedAgent: 'micromanager',
      reason: 'General commands and task coordination',
      suggestions: ['analyze', 'implement', 'generate', 'process']
    };
  }

  // ✅ Task 80: LLM Response Caching Methods

  /**
   * Cache an LLM response for this agent
   */
  protected async cacheLLMResponse(
    taskId: string,
    content: string,
    tokensUsed: number = 0,
    model: string = '',
    provider: string = '',
    finishReason: string = 'stop',
    responseTime: number = 0,
    contextHash: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await llmResponseCache.cacheResponse(
      this.config.id,
      taskId,
      content,
      tokensUsed,
      model,
      provider,
      finishReason,
      responseTime,
      contextHash,
      metadata
    );
  }

  /**
   * Retrieve a cached LLM response for this agent
   */
  protected async getCachedLLMResponse(taskId: string): Promise<string | null> {
    return await llmResponseCache.getCachedResponse(this.config.id, taskId);
  }

  /**
   * Check if an LLM response is cached for this agent
   */
  protected hasCachedLLMResponse(taskId: string): boolean {
    return llmResponseCache.hasCachedResponse(this.config.id, taskId);
  }

  /**
   * Generate a context hash for cache key generation
   */
  protected generateContextHash(context: AgentContext): string {
    const hashInput = JSON.stringify({
      task: context.task,
      files: context.files,
      codeContext: context.codeContext,
      rules: context.rules,
      dependencies: context.dependencies,
      // Exclude metadata to avoid cache misses on non-essential data
    });

    // Simple hash function (for production, consider using a proper hash library)
    let hash = 0;
    for (let i = 0; i < hashInput.length; i++) {
      const char = hashInput.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  // ✅ Task 82: Shared Context Memory Methods

  /**
   * Store context data for sharing with other agents
   */
  protected async storeSharedContext(
    taskId: string,
    contextType: ContextMemoryEntry['contextType'],
    data: Record<string, any>,
    options?: {
      tags?: string[];
      parentTaskId?: string;
      linkedTaskIds?: string[];
      ttl?: number;
      metadata?: Record<string, any>;
    }
  ): Promise<string> {
    return await agentContextMemory.storeContext(
      this.config.id,
      taskId,
      contextType,
      data,
      options
    );
  }

  /**
   * Retrieve shared context data from other agents
   */
  protected async getSharedContext(
    agentId: string,
    taskId: string,
    contextType?: string
  ): Promise<Record<string, any> | null> {
    return await agentContextMemory.getContext(agentId, taskId, contextType);
  }

  /**
   * Get context from the entire task chain (Micromanager → Architect → Intern)
   */
  protected async getChainContext(
    taskId: string,
    contextTypes?: string[]
  ): Promise<Record<string, any>> {
    return await agentContextMemory.getChainContext(taskId, contextTypes);
  }

  /**
   * Get a summary of available context for this agent
   */
  protected getContextSummary(taskId?: string): Record<string, any> {
    return agentContextMemory.getContextSummary(this.config.id, taskId);
  }

  /**
   * Query context with flexible criteria
   */
  protected querySharedContext(query: {
    agentId?: string;
    taskId?: string;
    parentTaskId?: string;
    contextType?: string;
    tags?: string[];
    timeRange?: { start: number; end: number };
    includeExpired?: boolean;
  }): ContextMemoryEntry[] {
    return agentContextMemory.queryContext(query);
  }

  /**
   * Clear context data for cleanup
   */
  protected async clearSharedContext(taskId?: string): Promise<number> {
    return await agentContextMemory.clearContext(this.config.id, taskId);
  }
}