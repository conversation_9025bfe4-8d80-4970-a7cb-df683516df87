// ✅ Task 101: Build-in Agent Terminal Session Manager
// Handles multiple parallel PTY sessions per agent with isolated state

import { IPty, spawn } from 'node-pty';
import * as os from 'os';

export interface SessionEntry {
  sessionId: string;
  ptyProcess: IPty;
  agentId: string;
  createdAt: number;
  lastUsed: number;
  workingDirectory: string;
  environment: Record<string, string>;
  isActive: boolean;
  commandCount: number;
  shell: string;
}

export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  sessionsByAgent: Record<string, number>;
  oldestSession: number;
  newestSession: number;
}

export class TerminalSessionManager {
  private sessions: Map<string, SessionEntry> = new Map();
  private maxSessionsPerAgent: number = 10;
  private maxTotalSessions: number = 50;
  private sessionTimeoutMs: number = 3600000; // 1 hour

  /**
   * ✅ Task 101 Step 1: Create isolated PTY session for agent
   */
  createSession(
    sessionId: string, 
    agentId: string, 
    options?: {
      shell?: string;
      workingDirectory?: string;
      environment?: Record<string, string>;
      cols?: number;
      rows?: number;
    }
  ): IPty {
    // Check if session already exists
    if (this.sessions.has(sessionId)) {
      throw new Error(`Session ${sessionId} already exists`);
    }

    // Check agent session limits
    const agentSessions = this.getSessionsByAgent(agentId);
    if (agentSessions.length >= this.maxSessionsPerAgent) {
      // Clean up oldest session for this agent
      const oldestSession = agentSessions.sort((a, b) => a.lastUsed - b.lastUsed)[0];
      this.destroySession(oldestSession.sessionId);
      console.log(`🧹 TerminalSessionManager: Cleaned up oldest session ${oldestSession.sessionId} for agent ${agentId}`);
    }

    // Check total session limits
    if (this.sessions.size >= this.maxTotalSessions) {
      this.cleanupOldSessions();
    }

    // Determine shell
    const shell = options?.shell || this.getDefaultShell();
    
    // Determine working directory
    const workingDirectory = options?.workingDirectory || process.env.HOME || process.cwd();
    
    // Prepare environment
    const environment = { ...process.env, ...options?.environment };

    try {
      // Create PTY process
      const ptyProcess = spawn(shell, [], {
        name: 'xterm-color',
        cols: options?.cols || 80,
        rows: options?.rows || 30,
        cwd: workingDirectory,
        env: environment,
      });

      // Create session entry
      const session: SessionEntry = {
        sessionId,
        ptyProcess,
        agentId,
        createdAt: Date.now(),
        lastUsed: Date.now(),
        workingDirectory,
        environment,
        isActive: true,
        commandCount: 0,
        shell
      };

      // Store session
      this.sessions.set(sessionId, session);

      console.log(`✅ TerminalSessionManager: Created session ${sessionId} for agent ${agentId} (shell: ${shell})`);
      
      // Set up cleanup on process exit
      ptyProcess.onExit(() => {
        this.sessions.delete(sessionId);
        console.log(`🧹 TerminalSessionManager: Session ${sessionId} auto-cleaned on process exit`);
      });

      return ptyProcess;
    } catch (error) {
      console.error(`❌ TerminalSessionManager: Failed to create session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * ✅ Task 101: Get PTY process for session
   */
  getSession(sessionId: string): IPty | null {
    const session = this.sessions.get(sessionId);
    if (session) {
      // Update last used timestamp
      session.lastUsed = Date.now();
      return session.ptyProcess;
    }
    return null;
  }

  /**
   * ✅ Task 101: Get session metadata
   */
  getSessionInfo(sessionId: string): SessionEntry | null {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.lastUsed = Date.now();
      return { ...session }; // Return copy to prevent external modification
    }
    return null;
  }

  /**
   * ✅ Task 101: Destroy session and cleanup PTY
   */
  destroySession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`⚠️ TerminalSessionManager: Session ${sessionId} not found for destruction`);
      return false;
    }

    try {
      // Kill PTY process
      session.ptyProcess.kill();
      
      // Remove from sessions map
      this.sessions.delete(sessionId);
      
      console.log(`✅ TerminalSessionManager: Destroyed session ${sessionId} for agent ${session.agentId}`);
      return true;
    } catch (error) {
      console.error(`❌ TerminalSessionManager: Error destroying session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Task 101: List all sessions
   */
  listSessions(): SessionEntry[] {
    return Array.from(this.sessions.values()).map(session => ({ ...session }));
  }

  /**
   * ✅ Task 101: Get sessions for specific agent
   */
  getSessionsByAgent(agentId: string): SessionEntry[] {
    return Array.from(this.sessions.values())
      .filter(session => session.agentId === agentId)
      .map(session => ({ ...session }));
  }

  /**
   * ✅ Task 101: Write data to session
   */
  writeToSession(sessionId: string, data: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`⚠️ TerminalSessionManager: Session ${sessionId} not found for write`);
      return false;
    }

    try {
      session.ptyProcess.write(data);
      session.lastUsed = Date.now();
      session.commandCount++;
      return true;
    } catch (error) {
      console.error(`❌ TerminalSessionManager: Error writing to session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Task 101: Resize session terminal
   */
  resizeSession(sessionId: string, cols: number, rows: number): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`⚠️ TerminalSessionManager: Session ${sessionId} not found for resize`);
      return false;
    }

    try {
      session.ptyProcess.resize(cols, rows);
      session.lastUsed = Date.now();
      return true;
    } catch (error) {
      console.error(`❌ TerminalSessionManager: Error resizing session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Task 101: Cleanup old inactive sessions
   */
  cleanupOldSessions(): number {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastUsed > this.sessionTimeoutMs) {
        this.destroySession(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 TerminalSessionManager: Cleaned up ${cleanedCount} old sessions`);
    }

    return cleanedCount;
  }

  /**
   * ✅ Task 101: Destroy all sessions for agent
   */
  destroyAgentSessions(agentId: string): number {
    const agentSessions = this.getSessionsByAgent(agentId);
    let destroyedCount = 0;

    for (const session of agentSessions) {
      if (this.destroySession(session.sessionId)) {
        destroyedCount++;
      }
    }

    console.log(`🧹 TerminalSessionManager: Destroyed ${destroyedCount} sessions for agent ${agentId}`);
    return destroyedCount;
  }

  /**
   * ✅ Task 101: Get session statistics
   */
  getStats(): SessionStats {
    const sessions = Array.from(this.sessions.values());
    const sessionsByAgent: Record<string, number> = {};

    sessions.forEach(session => {
      sessionsByAgent[session.agentId] = (sessionsByAgent[session.agentId] || 0) + 1;
    });

    const timestamps = sessions.map(s => s.createdAt);

    return {
      totalSessions: sessions.length,
      activeSessions: sessions.filter(s => s.isActive).length,
      sessionsByAgent,
      oldestSession: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      newestSession: timestamps.length > 0 ? Math.max(...timestamps) : 0
    };
  }

  /**
   * ✅ Task 101: Get default shell for platform
   */
  private getDefaultShell(): string {
    const platform = os.platform();
    
    if (platform === 'win32') {
      return process.env.COMSPEC || 'powershell.exe';
    } else {
      return process.env.SHELL || 'bash';
    }
  }

  /**
   * ✅ Task 101: Cleanup all sessions (for app shutdown)
   */
  destroyAllSessions(): number {
    const sessionCount = this.sessions.size;
    
    for (const sessionId of this.sessions.keys()) {
      this.destroySession(sessionId);
    }

    console.log(`🧹 TerminalSessionManager: Destroyed all ${sessionCount} sessions`);
    return sessionCount;
  }

  /**
   * ✅ Task 101: Check if session exists
   */
  hasSession(sessionId: string): boolean {
    return this.sessions.has(sessionId);
  }

  /**
   * ✅ Task 101: Get session count for agent
   */
  getAgentSessionCount(agentId: string): number {
    return Array.from(this.sessions.values())
      .filter(session => session.agentId === agentId).length;
  }
}

// Export singleton instance
export const terminalSessionManager = new TerminalSessionManager();
